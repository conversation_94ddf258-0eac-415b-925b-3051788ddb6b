package com.gtyyj;

import org.apache.commons.lang3.StringUtils;

/**
 * 数据脱敏工具类
 * 提供通用脱敏方法和常见敏感信息的专用脱敏方法
 */
public class DesensitizationUtils {

    /**
     * 通用脱敏方法
     *
     * @param originalText 原始文本
     * @param maskChar     脱敏替换字符
     * @param prefixLength 前面保留长度
     * @param suffixLength 后面保留长度
     * @return 脱敏后的文本
     */
    public static String mask(String originalText, char maskChar, int prefixLength, int suffixLength) {
        // 处理空值或空字符串
        if (StringUtils.isBlank(originalText)) {
            return originalText;
        }
        
        int totalLength = originalText.length();
        
        // 处理长度参数为2的特殊情况：只保留第一个字符
        if (prefixLength == 2 && suffixLength == 2) {
            if (totalLength == 1) {
                return originalText; // 只有一个字符，直接返回
            } else {
                // 保留第一个字符，其余用掩码字符替换
                StringBuilder masked = new StringBuilder();
                masked.append(originalText.charAt(0));
                for (int i = 1; i < totalLength; i++) {
                    masked.append(maskChar);
                }
                return masked.toString();
            }
        }
        
        // 常规情况处理：计算需要掩码的区间
        int maskStart = Math.min(prefixLength, totalLength);
        int maskEnd = Math.max(totalLength - suffixLength, maskStart); // 确保结束位置不小于开始位置
        
        // 如果保留区域已经覆盖或超出整个字符串，直接返回原始文本
        if (maskStart >= totalLength || maskEnd <= maskStart) {
            return originalText;
        }
        
        // 构建脱敏后的字符串
        StringBuilder maskedText = new StringBuilder();
        
        // 添加前段保留部分
        if (maskStart > 0) {
            maskedText.append(originalText, 0, maskStart);
        }
        
        // 添加掩码部分
        for (int i = maskStart; i < maskEnd; i++) {
            maskedText.append(maskChar);
        }
        
        // 添加后段保留部分
        if (suffixLength > 0 && totalLength - suffixLength >= maskEnd) {
            maskedText.append(originalText, totalLength - suffixLength, totalLength);
        }
        
        return maskedText.toString();
    }

    /**
     * 手机号脱敏（保留前3位和后4位）
     * 例如：13812345678 → 138****5678
     */
    public static String maskPhone(String phone) {
        return mask(phone, '*', 3, 4);
    }

    /**
     * 身份证号脱敏（保留前6位和后4位）
     * 例如：110101199003077774 → 110101********7774
     */
    public static String maskIdCard(String idCard) {
        return mask(idCard, '*', 6, 4);
    }

    /**
     * 银行卡号脱敏（保留前6位和后4位）
     * 例如：6222021234567890123 → 622202*******0123
     */
    public static String maskBankCard(String bankCard) {
        return mask(bankCard, '*', 6, 4);
    }

    /**
     * 邮箱脱敏（保留前3位和域名）
     * 例如：<EMAIL> → zha****@example.com
     */
    public static String maskEmail(String email) {
        if (StringUtils.isBlank(email) || !email.contains("@")) {
            return email;
        }
        
        int atIndex = email.indexOf('@');
        String prefix = email.substring(0, atIndex);
        String domain = email.substring(atIndex);
        
        // 对前缀部分进行脱敏
        String maskedPrefix = mask(prefix, '*', 3, 0);
        return maskedPrefix + domain;
    }

    /**
     * 中文姓名脱敏（保留姓氏）
     * 例如：张三 → 张*
     */
    public static String maskChineseName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        
        if (name.length() == 1) {
            return name;
        }
        
        return mask(name, '*', 1, 1);
    }

    public static void main(String[] args) {
        System.out.println(maskChineseName("张三"));
    }
}